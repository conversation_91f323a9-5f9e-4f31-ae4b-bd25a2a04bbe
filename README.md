# 沉浸式艺术滚动体验

一个基于 Vue 3 + Canvas 的沉浸式滚动动画网页，灵感来源于 Adaline.ai。随着页面滚动，用户可以体验到不同的艺术场景，就像置身在画中一样。

## ✨ 特性

- 🎨 **五个独特的艺术场景**：森林、山峦、海洋、夕阳、银河
- 🌟 **动态粒子系统**：每个场景都有独特的粒子效果（叶子、雪花、气泡、火花、星星）
- 🖱️ **鼠标交互**：粒子会对鼠标移动做出反应
- 📱 **响应式设计**：适配各种屏幕尺寸
- 🎯 **滚动进度指示器**：显示当前浏览进度
- 🎪 **场景指示器**：显示当前所在的艺术场景
- ⚡ **流畅的动画**：60fps 的 Canvas 动画
- 🎭 **沉浸式体验**：全屏背景动画配合内容滚动

## 🚀 快速开始

### 安装依赖
```bash
npm install
# 或
pnpm install
```

### 启动开发服务器
```bash
npm run dev
# 或
pnpm dev
```

### 构建生产版本
```bash
npm run build
# 或
pnpm build
```

## 🎨 场景介绍

1. **森林深处** - 绿色主题，飘落的叶子和摇摆的树木
2. **山峦之巅** - 蓝色主题，雪花飞舞和云雾缭绕
3. **海洋深蓝** - 海洋主题，上升的气泡和波浪效果
4. **夕阳西下** - 暖色主题，闪烁的火花和太阳光芒
5. **星河璀璨** - 宇宙主题，旋转的星系和星云效果

## 🛠️ 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **Vite** - 下一代前端构建工具
- **Canvas API** - 2D 图形渲染
- **CSS3** - 现代样式和动画
- **JavaScript ES6+** - 现代 JavaScript 特性

## 📱 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 🎯 使用方法

1. 打开网页后会看到加载动画
2. 加载完成后开始滚动页面
3. 随着滚动，背景会切换不同的艺术场景
4. 移动鼠标可以与粒子系统互动
5. 右侧的场景指示器显示当前场景
6. 顶部的进度条显示滚动进度

## 🎨 自定义

你可以通过修改 `src/components/ImmersiveCanvas.vue` 中的 `scenes` 配置来自定义场景：

```javascript
const scenes = [
  {
    name: 'your-scene',
    colors: ['#color1', '#color2', '#color3'],
    particleCount: 50,
    bgGradient: ['#bg1', '#bg2', '#bg3'],
    particleType: 'your-type'
  }
]
```

## 📄 许可证

MIT License
