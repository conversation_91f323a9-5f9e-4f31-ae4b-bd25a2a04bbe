<template>
  <div class="immersive-container">
    <!-- Loading indicator -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
        <p class="loading-text">正在加载艺术世界...</p>
      </div>
    </div>

    <!-- Scroll progress indicator -->
    <div class="scroll-progress">
      <div class="progress-bar" :style="{ width: scrollProgress * 100 + '%' }"></div>
    </div>

    <!-- Scene indicator -->
    <div class="scene-indicator">
      <div class="scene-dots">
        <div
          v-for="(scene, index) in scenes"
          :key="index"
          class="scene-dot"
          :class="{ active: index === currentScene }"
        >
          <span class="scene-name">{{ scene.name }}</span>
        </div>
      </div>
    </div>

    <!-- Help tooltip -->
    <div class="help-tooltip">
      <div class="help-icon">?</div>
      <div class="help-content">
        <h4>控制说明</h4>
        <p>↑↓ 或 ←→ : 切换场景</p>
        <p>空格键: 暂停/恢复动画</p>
        <p>鼠标移动: 与粒子互动</p>
        <p>滚动: 浏览内容</p>
      </div>
    </div>

    <!-- Canvas for the main animation -->
    <canvas
      ref="canvas"
      class="main-canvas"
      :width="canvasWidth"
      :height="canvasHeight"
    ></canvas>
    
    <!-- Content sections for scrolling -->
    <div class="content-sections">
      <section class="intro-section">
        <div class="text-content">
          <h1 class="title">沉浸式艺术之旅</h1>
          <p class="subtitle">随着滚动，探索美丽的艺术世界</p>
        </div>
      </section>
      
      <section class="story-section">
        <div class="text-content">
          <h2>第一章：森林深处</h2>
          <p>在这片神秘的森林中，每一片叶子都在诉说着古老的故事...</p>
        </div>
      </section>
      
      <section class="story-section">
        <div class="text-content">
          <h2>第二章：山峦之巅</h2>
          <p>云雾缭绕的山峰之上，隐藏着无数的秘密...</p>
        </div>
      </section>
      
      <section class="story-section">
        <div class="text-content">
          <h2>第三章：海洋深蓝</h2>
          <p>深邃的海洋中，光影交错，生命在此绽放...</p>
        </div>
      </section>

      <section class="story-section">
        <div class="text-content">
          <h2>第四章：夕阳西下</h2>
          <p>金色的夕阳洒向大地，温暖的光芒照亮心灵...</p>
        </div>
      </section>

      <section class="story-section">
        <div class="text-content">
          <h2>第五章：星河璀璨</h2>
          <p>在无垠的宇宙中，我们探索着星辰的奥秘...</p>
        </div>
      </section>

      <section class="final-section">
        <div class="text-content">
          <h2>旅程的终点</h2>
          <p>感谢您的陪伴，这场艺术之旅永不结束...</p>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const canvas = ref(null)
const canvasWidth = ref(window.innerWidth)
const canvasHeight = ref(window.innerHeight)
const isLoading = ref(true)

let ctx = null
let animationId = null
let scrollProgress = 0
let particles = []
let currentScene = 0
let mouseX = 0
let mouseY = 0
let mouseInfluence = 100
let sceneTransition = 0
let targetScene = 0

// 场景配置
const scenes = [
  {
    name: 'forest',
    colors: ['#2d5016', '#4a7c59', '#6b8e23', '#8fbc8f', '#98fb98', '#32cd32'],
    particleCount: 60,
    bgGradient: ['#0a1a0a', '#1a3d0a', '#2d5016'],
    particleType: 'leaf'
  },
  {
    name: 'mountain',
    colors: ['#4682b4', '#708090', '#b0c4de', '#e6e6fa', '#f0f8ff', '#87ceeb'],
    particleCount: 40,
    bgGradient: ['#1a1a2e', '#2c3e50', '#4682b4'],
    particleType: 'snow'
  },
  {
    name: 'ocean',
    colors: ['#006994', '#1e90ff', '#00bfff', '#87ceeb', '#b0e0e6', '#40e0d0'],
    particleCount: 80,
    bgGradient: ['#000428', '#001f3f', '#006994'],
    particleType: 'bubble'
  },
  {
    name: 'sunset',
    colors: ['#ff6b35', '#f7931e', '#ffd700', '#ff69b4', '#ff1493', '#ff4500'],
    particleCount: 50,
    bgGradient: ['#2c1810', '#ff4500', '#ff6b35'],
    particleType: 'spark'
  },
  {
    name: 'galaxy',
    colors: ['#9400d3', '#4b0082', '#0000ff', '#00ffff', '#ffffff', '#ffd700'],
    particleCount: 100,
    bgGradient: ['#000000', '#1a0033', '#330066'],
    particleType: 'star'
  }
]

// 增强的粒子类
class Particle {
  constructor(scene) {
    this.type = scene.particleType
    this.reset(scene)
    this.life = Math.random()
    this.rotation = Math.random() * Math.PI * 2
    this.rotationSpeed = (Math.random() - 0.5) * 0.1
  }

  reset(scene) {
    this.x = Math.random() * canvasWidth.value
    this.y = Math.random() * canvasHeight.value

    // 根据粒子类型设置不同的运动模式
    switch (this.type) {
      case 'leaf':
        this.vx = (Math.random() - 0.5) * 1
        this.vy = Math.random() * 0.5 + 0.2
        break
      case 'snow':
        this.vx = (Math.random() - 0.5) * 0.5
        this.vy = Math.random() * 1 + 0.5
        break
      case 'bubble':
        this.vx = (Math.random() - 0.5) * 1
        this.vy = -(Math.random() * 1 + 0.5)
        break
      case 'spark':
        this.vx = (Math.random() - 0.5) * 3
        this.vy = (Math.random() - 0.5) * 3
        break
      case 'star':
        this.vx = (Math.random() - 0.5) * 0.2
        this.vy = (Math.random() - 0.5) * 0.2
        break
      default:
        this.vx = (Math.random() - 0.5) * 2
        this.vy = (Math.random() - 0.5) * 2
    }

    this.size = Math.random() * 4 + 1
    this.color = scene.colors[Math.floor(Math.random() * scene.colors.length)]
    this.opacity = Math.random() * 0.8 + 0.2
    this.life = 1
  }

  update(scene) {
    // 鼠标交互效果
    const dx = mouseX - this.x
    const dy = mouseY - this.y
    const distance = Math.sqrt(dx * dx + dy * dy)

    if (distance < mouseInfluence) {
      const force = (mouseInfluence - distance) / mouseInfluence
      const angle = Math.atan2(dy, dx)
      this.vx -= Math.cos(angle) * force * 0.5
      this.vy -= Math.sin(angle) * force * 0.5
    }

    this.x += this.vx
    this.y += this.vy
    this.rotation += this.rotationSpeed
    this.life -= 0.003

    // 特殊运动效果
    if (this.type === 'leaf') {
      this.x += Math.sin(Date.now() * 0.001 + this.y * 0.01) * 0.5
    } else if (this.type === 'bubble') {
      this.x += Math.sin(Date.now() * 0.002 + this.y * 0.01) * 0.3
    }

    // 速度衰减
    this.vx *= 0.99
    this.vy *= 0.99

    // 边界检查
    if (this.x < -50 || this.x > canvasWidth.value + 50 ||
        this.y < -50 || this.y > canvasHeight.value + 50 ||
        this.life <= 0) {
      this.reset(scene)
    }
  }

  draw(ctx) {
    ctx.save()
    ctx.globalAlpha = this.opacity * this.life
    ctx.translate(this.x, this.y)
    ctx.rotate(this.rotation)

    switch (this.type) {
      case 'leaf':
        this.drawLeaf(ctx)
        break
      case 'snow':
        this.drawSnowflake(ctx)
        break
      case 'bubble':
        this.drawBubble(ctx)
        break
      case 'spark':
        this.drawSpark(ctx)
        break
      case 'star':
        this.drawStar(ctx)
        break
      default:
        this.drawDefault(ctx)
    }

    ctx.restore()
  }

  drawLeaf(ctx) {
    ctx.fillStyle = this.color
    ctx.beginPath()
    ctx.ellipse(0, 0, this.size, this.size * 0.6, 0, 0, Math.PI * 2)
    ctx.fill()
  }

  drawSnowflake(ctx) {
    ctx.strokeStyle = this.color
    ctx.lineWidth = 1
    for (let i = 0; i < 6; i++) {
      ctx.beginPath()
      ctx.moveTo(0, 0)
      ctx.lineTo(0, -this.size)
      ctx.stroke()
      ctx.rotate(Math.PI / 3)
    }
  }

  drawBubble(ctx) {
    ctx.strokeStyle = this.color
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.arc(0, 0, this.size, 0, Math.PI * 2)
    ctx.stroke()
  }

  drawSpark(ctx) {
    ctx.fillStyle = this.color
    ctx.beginPath()
    ctx.moveTo(0, -this.size)
    ctx.lineTo(this.size * 0.3, 0)
    ctx.lineTo(0, this.size)
    ctx.lineTo(-this.size * 0.3, 0)
    ctx.closePath()
    ctx.fill()
  }

  drawStar(ctx) {
    ctx.fillStyle = this.color
    ctx.beginPath()
    for (let i = 0; i < 5; i++) {
      const angle = (i * 4 * Math.PI) / 5
      const x = Math.cos(angle) * this.size
      const y = Math.sin(angle) * this.size
      if (i === 0) ctx.moveTo(x, y)
      else ctx.lineTo(x, y)
    }
    ctx.closePath()
    ctx.fill()
  }

  drawDefault(ctx) {
    ctx.fillStyle = this.color
    ctx.beginPath()
    ctx.arc(0, 0, this.size, 0, Math.PI * 2)
    ctx.fill()
  }
}

// 初始化粒子
const initParticles = () => {
  particles = []
  const scene = scenes[currentScene]
  for (let i = 0; i < scene.particleCount; i++) {
    particles.push(new Particle(scene))
  }
}

// 绘制背景渐变
const drawBackground = () => {
  const scene = scenes[currentScene]
  const gradient = ctx.createLinearGradient(0, 0, 0, canvasHeight.value)

  // 支持多色渐变
  const colors = scene.bgGradient
  for (let i = 0; i < colors.length; i++) {
    gradient.addColorStop(i / (colors.length - 1), colors[i])
  }

  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, canvasWidth.value, canvasHeight.value)

  // 添加动态光效
  const time = Date.now() * 0.001
  const lightGradient = ctx.createRadialGradient(
    canvasWidth.value * 0.5,
    canvasHeight.value * 0.3,
    0,
    canvasWidth.value * 0.5,
    canvasHeight.value * 0.3,
    canvasWidth.value * 0.8
  )

  lightGradient.addColorStop(0, `rgba(255, 255, 255, ${0.05 + Math.sin(time) * 0.02})`)
  lightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)')

  ctx.fillStyle = lightGradient
  ctx.fillRect(0, 0, canvasWidth.value, canvasHeight.value)
}

// 绘制艺术形状
const drawArtShapes = () => {
  const scene = scenes[currentScene]
  const time = Date.now() * 0.001

  ctx.save()
  ctx.globalAlpha = 0.4 + Math.sin(time * 0.5) * 0.1

  // 根据不同场景绘制不同的艺术形状
  switch (scene.name) {
    case 'forest':
      drawTrees(time)
      drawFloatingLeaves(time)
      break
    case 'mountain':
      drawMountains(time)
      drawClouds(time)
      break
    case 'ocean':
      drawWaves(time)
      drawBubbles(time)
      break
    case 'sunset':
      drawSun(time)
      drawRays(time)
      break
    case 'galaxy':
      drawGalaxy(time)
      drawNebula(time)
      break
  }

  ctx.restore()
}

const drawTrees = (time) => {
  for (let i = 0; i < 5; i++) {
    const x = (canvasWidth.value / 6) * (i + 1)
    const y = canvasHeight.value * 0.8
    const height = 100 + Math.sin(time + i) * 20
    
    ctx.fillStyle = '#2d5016'
    ctx.fillRect(x - 10, y - height, 20, height)
    
    ctx.fillStyle = '#4a7c59'
    ctx.beginPath()
    ctx.arc(x, y - height, 30, 0, Math.PI * 2)
    ctx.fill()
  }
}

const drawMountains = (time) => {
  ctx.fillStyle = '#708090'
  ctx.beginPath()
  ctx.moveTo(0, canvasHeight.value)
  
  for (let x = 0; x <= canvasWidth.value; x += 50) {
    const y = canvasHeight.value * 0.6 + Math.sin(x * 0.01 + time) * 50
    ctx.lineTo(x, y)
  }
  
  ctx.lineTo(canvasWidth.value, canvasHeight.value)
  ctx.closePath()
  ctx.fill()
}

const drawWaves = (time) => {
  for (let i = 0; i < 3; i++) {
    ctx.strokeStyle = scenes[2].colors[i]
    ctx.lineWidth = 3
    ctx.beginPath()
    
    const yOffset = canvasHeight.value * 0.5 + i * 30
    ctx.moveTo(0, yOffset)
    
    for (let x = 0; x <= canvasWidth.value; x += 10) {
      const y = yOffset + Math.sin(x * 0.02 + time + i) * 20
      ctx.lineTo(x, y)
    }
    
    ctx.stroke()
  }
}

const drawSun = (time) => {
  const centerX = canvasWidth.value * 0.5
  const centerY = canvasHeight.value * 0.3
  const radius = 80 + Math.sin(time) * 10

  const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius)
  gradient.addColorStop(0, '#ffd700')
  gradient.addColorStop(1, '#ff6b35')

  ctx.fillStyle = gradient
  ctx.beginPath()
  ctx.arc(centerX, centerY, radius, 0, Math.PI * 2)
  ctx.fill()
}

// 新增的艺术效果函数
const drawFloatingLeaves = (time) => {
  for (let i = 0; i < 8; i++) {
    const x = (canvasWidth.value / 10) * i + Math.sin(time + i) * 50
    const y = canvasHeight.value * 0.3 + Math.cos(time * 0.7 + i) * 100
    const size = 8 + Math.sin(time + i) * 3

    ctx.save()
    ctx.translate(x, y)
    ctx.rotate(time + i)
    ctx.fillStyle = '#6b8e23'
    ctx.globalAlpha = 0.6

    // 绘制叶子形状
    ctx.beginPath()
    ctx.ellipse(0, 0, size, size * 0.6, 0, 0, Math.PI * 2)
    ctx.fill()
    ctx.restore()
  }
}

const drawClouds = (time) => {
  for (let i = 0; i < 4; i++) {
    const x = (canvasWidth.value / 5) * i + Math.sin(time * 0.3 + i) * 30
    const y = canvasHeight.value * 0.2 + Math.sin(time * 0.2 + i) * 20

    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)'

    // 绘制云朵
    for (let j = 0; j < 5; j++) {
      const cloudX = x + j * 15
      const cloudY = y + Math.sin(j + time) * 5
      const radius = 20 + Math.sin(time + j) * 5

      ctx.beginPath()
      ctx.arc(cloudX, cloudY, radius, 0, Math.PI * 2)
      ctx.fill()
    }
  }
}

const drawBubbles = (time) => {
  for (let i = 0; i < 15; i++) {
    const x = Math.random() * canvasWidth.value
    const y = canvasHeight.value * 0.7 + Math.sin(time + i) * 200
    const radius = 5 + Math.sin(time * 2 + i) * 8

    ctx.strokeStyle = 'rgba(135, 206, 235, 0.5)'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.arc(x, y, radius, 0, Math.PI * 2)
    ctx.stroke()
  }
}

const drawRays = (time) => {
  const centerX = canvasWidth.value * 0.5
  const centerY = canvasHeight.value * 0.3

  ctx.strokeStyle = 'rgba(255, 215, 0, 0.3)'
  ctx.lineWidth = 3

  for (let i = 0; i < 12; i++) {
    const angle = (i / 12) * Math.PI * 2 + time * 0.5
    const length = 150 + Math.sin(time + i) * 30

    const startX = centerX + Math.cos(angle) * 90
    const startY = centerY + Math.sin(angle) * 90
    const endX = centerX + Math.cos(angle) * length
    const endY = centerY + Math.sin(angle) * length

    ctx.beginPath()
    ctx.moveTo(startX, startY)
    ctx.lineTo(endX, endY)
    ctx.stroke()
  }
}

// 银河场景绘制函数
const drawGalaxy = (time) => {
  const centerX = canvasWidth.value * 0.5
  const centerY = canvasHeight.value * 0.5

  // 绘制螺旋星系
  ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
  ctx.lineWidth = 2

  for (let arm = 0; arm < 3; arm++) {
    ctx.beginPath()
    for (let i = 0; i < 200; i++) {
      const angle = (i * 0.1) + (arm * Math.PI * 2 / 3) + time * 0.2
      const radius = i * 2 + Math.sin(time + i * 0.1) * 10

      const x = centerX + Math.cos(angle) * radius
      const y = centerY + Math.sin(angle) * radius

      if (i === 0) ctx.moveTo(x, y)
      else ctx.lineTo(x, y)
    }
    ctx.stroke()
  }

  // 绘制中心黑洞
  const blackHoleGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, 50)
  blackHoleGradient.addColorStop(0, 'rgba(0, 0, 0, 1)')
  blackHoleGradient.addColorStop(0.7, 'rgba(75, 0, 130, 0.8)')
  blackHoleGradient.addColorStop(1, 'rgba(148, 0, 211, 0.3)')

  ctx.fillStyle = blackHoleGradient
  ctx.beginPath()
  ctx.arc(centerX, centerY, 50 + Math.sin(time * 2) * 5, 0, Math.PI * 2)
  ctx.fill()
}

const drawNebula = (time) => {
  // 绘制星云效果
  for (let i = 0; i < 5; i++) {
    const x = (canvasWidth.value / 6) * (i + 1) + Math.sin(time * 0.3 + i) * 100
    const y = canvasHeight.value * 0.3 + Math.cos(time * 0.2 + i) * 80
    const radius = 80 + Math.sin(time + i) * 20

    const nebulaGradient = ctx.createRadialGradient(x, y, 0, x, y, radius)
    const colors = ['rgba(148, 0, 211, 0.2)', 'rgba(75, 0, 130, 0.1)', 'rgba(0, 0, 255, 0.1)']
    const color = colors[i % colors.length]

    nebulaGradient.addColorStop(0, color)
    nebulaGradient.addColorStop(1, 'rgba(0, 0, 0, 0)')

    ctx.fillStyle = nebulaGradient
    ctx.beginPath()
    ctx.arc(x, y, radius, 0, Math.PI * 2)
    ctx.fill()
  }
}

// 动画循环
const animate = () => {
  ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)

  // 场景过渡动画
  if (sceneTransition > 0) {
    sceneTransition -= 0.02
    if (sceneTransition <= 0) {
      currentScene = targetScene
      sceneTransition = 0
    }
  }

  drawBackground()
  drawArtShapes()

  // 场景过渡效果
  if (sceneTransition > 0) {
    const alpha = sceneTransition
    ctx.fillStyle = `rgba(0, 0, 0, ${alpha})`
    ctx.fillRect(0, 0, canvasWidth.value, canvasHeight.value)
  }

  // 更新和绘制粒子
  const scene = scenes[currentScene]
  particles.forEach(particle => {
    particle.update(scene)
    particle.draw(ctx)
  })

  animationId = requestAnimationFrame(animate)
}

// 处理滚动事件
const handleScroll = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const scrollHeight = document.documentElement.scrollHeight - window.innerHeight
  scrollProgress = Math.min(scrollTop / scrollHeight, 1)

  // 根据滚动进度切换场景，使用更平滑的过渡
  const sceneProgress = scrollProgress * (scenes.length - 1)
  const newScene = Math.floor(sceneProgress)

  if (newScene !== currentScene && newScene < scenes.length) {
    targetScene = newScene
    sceneTransition = 0.5 // 开始过渡动画

    // 平滑过渡粒子
    const fadeProgress = sceneProgress - newScene
    particles.forEach(particle => {
      if (Math.random() < fadeProgress * 0.1) {
        particle.reset(scenes[targetScene])
      }
    })

    // 如果场景完全切换，重新初始化所有粒子
    if (fadeProgress < 0.1) {
      setTimeout(() => {
        initParticles()
      }, 250) // 延迟初始化，配合过渡动画
    }
  }
}

// 处理窗口大小变化
const handleResize = () => {
  canvasWidth.value = window.innerWidth
  canvasHeight.value = window.innerHeight

  if (canvas.value) {
    canvas.value.width = canvasWidth.value
    canvas.value.height = canvasHeight.value
  }
}

// 处理鼠标移动
const handleMouseMove = (event) => {
  mouseX = event.clientX
  mouseY = event.clientY
}

// 处理键盘事件
const handleKeyDown = (event) => {
  switch (event.key) {
    case 'ArrowUp':
    case 'ArrowLeft':
      event.preventDefault()
      if (currentScene > 0) {
        targetScene = currentScene - 1
        sceneTransition = 0.5
        // 滚动到对应位置
        const targetScroll = (targetScene / (scenes.length - 1)) * (document.documentElement.scrollHeight - window.innerHeight)
        window.scrollTo({ top: targetScroll, behavior: 'smooth' })
      }
      break
    case 'ArrowDown':
    case 'ArrowRight':
      event.preventDefault()
      if (currentScene < scenes.length - 1) {
        targetScene = currentScene + 1
        sceneTransition = 0.5
        // 滚动到对应位置
        const targetScroll = (targetScene / (scenes.length - 1)) * (document.documentElement.scrollHeight - window.innerHeight)
        window.scrollTo({ top: targetScroll, behavior: 'smooth' })
      }
      break
    case ' ': // 空格键暂停/恢复动画
      event.preventDefault()
      if (animationId) {
        cancelAnimationFrame(animationId)
        animationId = null
      } else {
        animate()
      }
      break
  }
}

onMounted(() => {
  if (canvas.value) {
    ctx = canvas.value.getContext('2d')
    canvas.value.width = canvasWidth.value
    canvas.value.height = canvasHeight.value

    initParticles()
    animate()

    window.addEventListener('scroll', handleScroll)
    window.addEventListener('resize', handleResize)
    window.addEventListener('mousemove', handleMouseMove)
    window.addEventListener('keydown', handleKeyDown)

    // 延迟隐藏加载指示器，让用户看到加载效果
    setTimeout(() => {
      isLoading.value = false
    }, 1500)
  }
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  window.removeEventListener('scroll', handleScroll)
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('mousemove', handleMouseMove)
  window.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.immersive-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #000000, #1a1a2e, #16213e);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeOut 0.5s ease-out 1s forwards;
}

.loading-spinner {
  text-align: center;
}

.spinner-ring {
  width: 80px;
  height: 80px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-top: 4px solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 2rem;
}

.loading-text {
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 300;
  letter-spacing: 2px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes fadeOut {
  to {
    opacity: 0;
    visibility: hidden;
  }
}

.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  z-index: 100;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #ff6b35, #f7931e, #ffd700);
  transition: width 0.1s ease;
}

.scene-indicator {
  position: fixed;
  top: 50%;
  right: 2rem;
  transform: translateY(-50%);
  z-index: 100;
}

.scene-dots {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.scene-dot {
  position: relative;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.scene-dot.active {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  transform: scale(1.5);
  box-shadow: 0 0 20px rgba(255, 107, 53, 0.6);
}

.scene-name {
  position: absolute;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.scene-dot:hover .scene-name,
.scene-dot.active .scene-name {
  opacity: 1;
}

.help-tooltip {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  z-index: 100;
}

.help-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;
}

.help-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(255, 107, 53, 0.5);
}

.help-content {
  position: absolute;
  bottom: 50px;
  left: 0;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 1rem;
  border-radius: 10px;
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.help-tooltip:hover .help-content {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.help-content h4 {
  margin: 0 0 0.5rem 0;
  color: #ff6b35;
  font-size: 1rem;
}

.help-content p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: #e0e0e0;
}

.main-canvas {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;
}

.content-sections {
  position: relative;
  z-index: 2;
  width: 100%;
}

.intro-section,
.story-section,
.final-section {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.text-content {
  max-width: 800px;
  text-align: center;
  background: rgba(0, 0, 0, 0.7);
  padding: 3rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.5rem;
  color: #e0e0e0;
  margin-bottom: 2rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

h2 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

p {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #d0d0d0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  .subtitle,
  p {
    font-size: 1rem;
  }

  .text-content {
    padding: 2rem;
    margin: 1rem;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #f7931e, #ffd700);
}

/* 动画效果 */
.text-content {
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 鼠标悬停效果 */
.text-content:hover {
  transform: translateY(-5px);
  transition: transform 0.3s ease;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}
</style>
